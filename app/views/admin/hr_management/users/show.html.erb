<div id="employee-user-show-container" class="min-h-screen bg-[#f5f5f7] dark:bg-[#1c1c1e]">
  <%= render 'admin/hr_management/header' %>

  <div class="w-full p-6">
    <!-- Back Button -->
    <div class="mb-6">
      <%= link_to admin_hr_management_users_path, class: "inline-flex items-center gap-2 text-[#6e6e73] dark:text-[#a1a1a6] hover:text-[#1d1d1f] dark:hover:text-white transition-colors" do %>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m15 18-6-6 6-6"/>
        </svg>
        Back to My Employees
      <% end %>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 w-full">
      <!-- Left Column - Profile Card -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-6">
          <!-- Practice Logo -->
          <div class="flex justify-center mb-4">
            <div class="w-16 h-16 bg-[#0071e3] rounded-2xl flex items-center justify-center">
              <span class="text-white font-bold text-xl">SM</span>
            </div>
          </div>

          <!-- User Info -->
          <div class="text-center mb-6">
            <h2 class="text-xl font-semibold text-[#1d1d1f] dark:text-white mb-1">
              <%= @user.full_name %>
            </h2>
            <p class="text-[#0071e3] text-sm font-medium mb-1">
              <%= @user.roles.first&.name&.titleize || 'Staff Member' %>
            </p>
            <p class="text-[#6e6e73] dark:text-[#a1a1a6] text-sm">
              <%= @user.roles.map(&:name).map(&:capitalize).join(', ').presence || 'General' %>
            </p>
          </div>

          <!-- Contact Info -->
          <div class="space-y-3 mb-6">
            <div class="flex items-center gap-3 text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <rect width="20" height="16" x="2" y="4" rx="2"/>
                <path d="m22 7-10 5L2 7"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white"><%= @user.email %></span>
            </div>
            <% if @user.mobile_phone.present? %>
              <div class="flex items-center gap-3 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                <span class="text-[#1d1d1f] dark:text-white"><%= @user.mobile_phone %></span>
              </div>
            <% end %>
            <div class="flex items-center gap-3 text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73]">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white">Joined on 15/08/2020</span>
            </div>
          </div>

          <!-- Send Message Button -->
          <button class="w-full bg-[#0071e3] hover:bg-[#0077ed] text-white rounded-xl py-3 px-4 font-medium transition-all duration-200 shadow-sm hover:shadow">
            Send Message
          </button>
        </div>
      </div>

      <!-- Right Column - Dashboard Cards -->
      <div class="lg:col-span-4">
        <!-- Stats Cards Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <!-- Leave Balance Card -->
          <%= render 'shared/circular_progress_card',
              title: 'Leave Balance',
              icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-days h-6 w-6">
                      <path d="M8 2v4"></path>
                      <path d="M16 2v4"></path>
                      <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                      <path d="M3 10h18"></path>
                      <path d="M8 14h.01"></path>
                      <path d="M12 14h.01"></path>
                      <path d="M16 14h.01"></path>
                      <path d="M8 18h.01"></path>
                      <path d="M12 18h.01"></path>
                      <path d="M16 18h.01"></path>
                    </svg>',
              total_value: 13,
              unit: 'days',
              segments: [
                { name: 'Sick', value: 3, color: '#fca5a5' },
                { name: 'Unpaid', value: 1, color: '#fde047' },
                { name: 'Remaining', value: 9, color: '#93c5fd' }
              ] %>

          <!-- Tasks Card -->
          <%= render 'shared/circular_progress_card',
              title: 'Tasks',
              icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clipboard-check h-6 w-6">
                      <rect width="8" height="4" x="8" y="2" rx="1" ry="1"/>
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                      <path d="m9 14 2 2 4-4"/>
                    </svg>',
              total_value: 100,
              unit: '%',
              segments: [
                { name: 'Completed', value: 65, color: '#86efac' },
                { name: 'In Progress', value: 30, color: '#93c5fd' },
                { name: 'Overdue', value: 5, color: '#fca5a5' }
              ] %>

          <!-- Attendance Card -->
          <%= render 'shared/circular_progress_card',
              title: 'Attendance',
              icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-6 w-6">
                      <circle cx="12" cy="12" r="10"/>
                      <polyline points="12,6 12,12 16,14"/>
                    </svg>',
              total_value: 100,
              unit: '%',
              segments: [
                { name: 'Present', value: 95, color: '#86efac' },
                { name: 'Late', value: 3, color: '#fde047' },
                { name: 'Absent', value: 2, color: '#fca5a5' }
              ] %>

          <!-- Training Status Card -->
          <%= render 'shared/circular_progress_card',
              title: 'Training Status',
              icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap h-6 w-6">
                      <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                      <path d="M6 12v5c3 3 9 3 12 0v-5"/>
                    </svg>',
              total_value: 100,
              unit: '%',
              segments: [
                { name: 'Completed', value: 80, color: '#86efac' },
                { name: 'In Progress', value: 15, color: '#93c5fd' },
                { name: 'Pending', value: 5, color: '#fde047' }
              ] %>
        </div>

        <!-- Action Buttons Row -->
        <div class="grid grid-cols-4 gap-4 mb-6">
          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Timesheets</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Payslips</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Documents</span>
            </div>
          </button>

          <button class="bg-white dark:bg-[#2c2c2e] rounded-xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] p-4 hover:shadow-md transition-all duration-200 group">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#6e6e73] group-hover:text-[#0071e3]">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="m22 2-5 10-5-5 10-5z"/>
              </svg>
              <span class="text-[#1d1d1f] dark:text-white font-medium">Permissions</span>
            </div>
          </button>
        </div>

        <!-- Calendar Section -->
        <div class="bg-white dark:bg-[#2c2c2e] rounded-2xl shadow-sm border border-[#e5e5e7] dark:border-[#3a3a3c] overflow-hidden">
          <!-- Calendar Header -->
          <div class="p-6 border-b border-[#e5e5e7] dark:border-[#3a3a3c]">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <div class="relative">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6e6e73]">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="m21 21-4.3-4.3"/>
                  </svg>
                  <input type="text" placeholder="Search employees" class="pl-10 pr-4 py-2 bg-[#f5f5f7] dark:bg-[#3a3a3c] border border-[#e5e5e5] dark:border-[#4c4c4e] rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#0071e3]">
                </div>
                <button class="px-4 py-2 bg-[#0071e3] text-white rounded-lg text-sm font-medium hover:bg-[#0077ed] transition-colors">
                  Today
                </button>
              </div>

              <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                  <button class="p-2 hover:bg-[#f5f5f7] dark:hover:bg-[#3a3a3c] rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m15 18-6-6 6-6"/>
                    </svg>
                  </button>
                  <span class="text-[#1d1d1f] dark:text-white font-medium">May 2025</span>
                  <button class="p-2 hover:bg-[#f5f5f7] dark:hover:bg-[#3a3a3c] rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </button>
                </div>

                <div class="flex bg-[#f5f5f7] dark:bg-[#3a3a3c] rounded-lg p-1">
                  <button class="px-3 py-1 text-sm text-[#6e6e73] dark:text-[#a1a1a6] hover:text-[#1d1d1f] dark:hover:text-white transition-colors">
                    Week
                  </button>
                  <button class="px-3 py-1 text-sm bg-[#0071e3] text-white rounded-md">
                    Month
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Calendar Content -->
          <div class="p-6">
            <!-- Calendar Grid would go here - simplified for now -->
            <div class="text-center py-12 text-[#6e6e73] dark:text-[#a1a1a6]">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-4 opacity-50">
                <path d="M8 2v4"/>
                <path d="M16 2v4"/>
                <rect width="18" height="18" x="3" y="4" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
              <p>Calendar view will be implemented here</p>
              <p class="text-sm mt-2">This would show the employee's schedule, shifts, and time off</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



