<%
  # Default values
  title ||= "Progress"
  icon ||= "circle"
  total_value ||= 100
  unit ||= "%"
  segments ||= []

  # Calculate total for percentage calculation
  segments_total = segments.sum { |s| s[:value] }

  # Calculate stroke-dasharray values for each segment
  circumference = 2 * Math::PI * 45 # radius of 45 for a 100x100 viewBox
  current_offset = 0

  segment_data = segments.map do |segment|
    percentage = segments_total > 0 ? (segment[:value].to_f / segments_total) : 0
    dash_length = circumference * percentage

    data = {
      name: segment[:name],
      value: segment[:value],
      color: segment[:color],
      percentage: percentage * 100,
      dash_length: dash_length,
      dash_offset: current_offset
    }

    current_offset += dash_length
    data
  end
%>

<div class="rounded-lg text-card-foreground shadow-sm flex flex-col bg-white dark:bg-slate-950 border border-slate-200 dark:border-slate-800">
  <!-- Header -->
  <div class="flex p-6 flex-row items-center gap-4 space-y-0 pb-2">
    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-300">
      <%= icon.html_safe %>
    </div>
    <div class="tracking-tight flex items-center justify-between text-lg font-semibold"><%= title %></div>
  </div>

  <!-- Chart Section -->
  <div class="p-6 flex-1 flex items-center justify-center relative py-4">
    <div class="mx-auto aspect-square h-[160px] w-[160px] relative">
      <!-- SVG Donut Chart -->
      <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
        <!-- Background circle -->
        <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" stroke-width="8"/>

        <!-- Progress segments -->
        <% segment_data.each_with_index do |segment, index| %>
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke="<%= segment[:color] %>"
            stroke-width="8"
            stroke-linecap="round"
            stroke-dasharray="<%= segment[:dash_length] %> <%= circumference %>"
            stroke-dashoffset="<%= -segment[:dash_offset] %>"
            style="transition: stroke-dasharray 0.3s ease;"
          />
        <% end %>
      </svg>

      <!-- Center text -->
      <div class="absolute inset-0 flex flex-col items-center justify-center" aria-hidden="true">
        <span class="text-3xl font-bold tracking-tighter"><%= total_value %></span>
        <span class="text-sm text-muted-foreground"><%= unit %></span>
      </div>
    </div>
  </div>

  <!-- Legend -->
  <div class="flex flex-col gap-1 text-sm p-4 pt-0">
    <% segments.each do |segment| %>
      <div class="flex items-center justify-between p-2 rounded-md hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
        <div class="flex items-center gap-2">
          <span class="h-2.5 w-2.5 shrink-0 rounded-full" aria-hidden="true" style="background-color: <%= segment[:color] %>;"></span>
          <span class="text-muted-foreground"><%= segment[:name] %></span>
        </div>
        <span class="font-semibold text-right"><%= segment[:value] %></span>
      </div>
    <% end %>
  </div>
</div>
